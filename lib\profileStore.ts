import { RiasecType, RiasecScores } from './types';
import { GeminiProfileService, GeminiProfileResponse } from './geminiService';

// Interface untuk profil interpretasi
export interface ProfileInterpretation {
  dominantTypes: RiasecType[];
  profileTitle: string;
  profileDescription: string;
  strengths: string[];
  careerSuggestions: string[];
  developmentAreas: string[];
  workEnvironment: string;
}

// Fungsi untuk mendapatkan interpretasi profil berdasarkan skor menggunakan Gemini AI
export async function getProfileInterpretation(scores: RiasecScores): Promise<ProfileInterpretation> {
  try {
    // Inisialisasi Gemini service
    const geminiService = new GeminiProfileService();

    // Generate profile menggunakan AI
    const aiProfile: GeminiProfileResponse = await geminiService.generateProfile(scores);

    // Hitung dominant types untuk development areas
    const sortedScores = Object.entries(scores)
      .sort(([,a], [,b]) => b - a)
      .map(([type, score]) => ({ type: type as RiasecType, score }));

    const maxScore = sortedScores[0].score;
    const dominantTypes = sortedScores
      .filter(item => item.score === maxScore)
      .map(item => item.type);

    // Generate development areas berdasarkan skor terendah (hanya jika skor < 4)
    const lowScoreTypes = sortedScores
      .filter(item => item.score < 4)
      .map(item => item.type);

    const developmentAreas = lowScoreTypes.map(type => {
      switch(type) {
        case 'R': return 'Keterampilan praktis dan teknis';
        case 'I': return 'Kemampuan analitis dan penelitian';
        case 'A': return 'Kreativitas dan ekspresi artistik';
        case 'S': return 'Keterampilan interpersonal dan empati';
        case 'E': return 'Kepemimpinan dan kemampuan persuasi';
        case 'C': return 'Organisasi dan perhatian terhadap detail';
        default: return 'Area pengembangan umum';
      }
    });

    return {
      dominantTypes,
      profileTitle: aiProfile.profileTitle,
      profileDescription: aiProfile.profileDescription,
      strengths: aiProfile.strengths,
      careerSuggestions: aiProfile.careerSuggestions,
      developmentAreas,
      workEnvironment: aiProfile.workStyle
    };
  } catch (error) {
    console.error('Error getting AI profile interpretation:', error);

    // Fallback ke metode lama jika AI gagal
    return getFallbackProfileInterpretation(scores);
  }
}

// Fungsi fallback sederhana jika Gemini AI gagal
function getFallbackProfileInterpretation(scores: RiasecScores): ProfileInterpretation {
  // Urutkan skor dari tertinggi ke terendah
  const sortedScores = Object.entries(scores)
    .sort(([,a], [,b]) => b - a)
    .map(([type, score]) => ({ type: type as RiasecType, score }));

  // Ambil skor tertinggi
  const maxScore = sortedScores[0].score;
  const dominantTypes = sortedScores
    .filter(item => item.score === maxScore)
    .map(item => item.type);

  // Generate development areas berdasarkan skor terendah (hanya jika skor < 4)
  const lowScoreTypes = sortedScores
    .filter(item => item.score < 4)
    .map(item => item.type);

  const developmentAreas = lowScoreTypes.map(type => {
    switch(type) {
      case 'R': return 'Keterampilan praktis dan teknis';
      case 'I': return 'Kemampuan analitis dan penelitian';
      case 'A': return 'Kreativitas dan ekspresi artistik';
      case 'S': return 'Keterampilan interpersonal dan empati';
      case 'E': return 'Kepemimpinan dan kemampuan persuasi';
      case 'C': return 'Organisasi dan perhatian terhadap detail';
      default: return 'Area pengembangan umum';
    }
  });

  // Fallback profile sederhana berdasarkan tipe dominan
  const primaryType = dominantTypes[0];
  const fallbackProfiles: Record<RiasecType, Omit<ProfileInterpretation, 'dominantTypes' | 'developmentAreas'>> = {
    'R': {
      profileTitle: 'Profil Praktis (Realistic)',
      profileDescription: 'Anda memiliki minat yang kuat pada aktivitas praktis dan hands-on. Anda cenderung menyukai pekerjaan yang melibatkan alat, mesin, atau aktivitas fisik.',
      strengths: ['Keterampilan teknis', 'Pemecahan masalah praktis', 'Kemandirian', 'Ketahanan fisik', 'Orientasi hasil'],
      careerSuggestions: ['Teknisi', 'Insinyur', 'Mekanik', 'Arsitek', 'Pilot'],
      workEnvironment: 'Lingkungan kerja yang terstruktur dengan tugas-tugas konkret dan hasil yang terukur.'
    },
    'I': {
      profileTitle: 'Profil Analitis (Investigative)',
      profileDescription: 'Anda memiliki minat yang kuat pada aktivitas analitis dan penelitian. Anda cenderung menyukai pemecahan masalah kompleks dan bekerja dengan ide-ide.',
      strengths: ['Kemampuan analitis', 'Pemikiran kritis', 'Penelitian', 'Pemecahan masalah', 'Objektivitas'],
      careerSuggestions: ['Peneliti', 'Ilmuwan', 'Dokter', 'Psikolog', 'Analis Data'],
      workEnvironment: 'Lingkungan kerja yang tenang dengan waktu untuk berpikir dan menganalisis secara mendalam.'
    },
    'A': {
      profileTitle: 'Profil Kreatif (Artistic)',
      profileDescription: 'Anda memiliki minat yang kuat pada aktivitas kreatif dan artistik. Anda cenderung menyukai ekspresi diri dan menciptakan sesuatu yang baru.',
      strengths: ['Kreativitas', 'Ekspresi diri', 'Inovasi', 'Sensitivitas estetika', 'Fleksibilitas'],
      careerSuggestions: ['Desainer', 'Seniman', 'Penulis', 'Musisi', 'Fotografer'],
      workEnvironment: 'Lingkungan kerja yang fleksibel dengan kebebasan untuk berekspresi dan berkreasi.'
    },
    'S': {
      profileTitle: 'Profil Sosial (Social)',
      profileDescription: 'Anda memiliki minat yang kuat pada aktivitas sosial dan membantu orang lain. Anda cenderung menyukai interaksi interpersonal dan membuat dampak positif.',
      strengths: ['Empati', 'Komunikasi', 'Kepemimpinan', 'Kerja tim', 'Motivasi orang lain'],
      careerSuggestions: ['Guru', 'Konselor', 'Perawat', 'Pekerja Sosial', 'HR Manager'],
      workEnvironment: 'Lingkungan kerja yang kolaboratif dengan banyak interaksi interpersonal yang bermakna.'
    },
    'E': {
      profileTitle: 'Profil Enterprising (Enterprising)',
      profileDescription: 'Anda memiliki minat yang kuat pada aktivitas kepemimpinan dan bisnis. Anda cenderung menyukai tantangan, persuasi, dan mempengaruhi orang lain.',
      strengths: ['Kepemimpinan', 'Persuasi', 'Pengambilan risiko', 'Orientasi hasil', 'Visi strategis'],
      careerSuggestions: ['Manajer', 'Pengusaha', 'Sales Manager', 'Konsultan', 'Direktur'],
      workEnvironment: 'Lingkungan kerja yang dinamis dengan peluang untuk memimpin dan mempengaruhi keputusan.'
    },
    'C': {
      profileTitle: 'Profil Konvensional (Conventional)',
      profileDescription: 'Anda memiliki minat yang kuat pada aktivitas yang terorganisir dan sistematis. Anda cenderung menyukai struktur, detail, dan prosedur yang jelas.',
      strengths: ['Organisasi', 'Perhatian detail', 'Keandalan', 'Efisiensi', 'Konsistensi'],
      careerSuggestions: ['Akuntan', 'Administrator', 'Auditor', 'Analis Keuangan', 'Sekretaris'],
      workEnvironment: 'Lingkungan kerja yang terstruktur dengan prosedur dan sistem yang jelas dan konsisten.'
    }
  };

  const fallbackProfile = fallbackProfiles[primaryType] || fallbackProfiles['R'];

  return {
    dominantTypes,
    profileTitle: fallbackProfile.profileTitle,
    profileDescription: fallbackProfile.profileDescription,
    strengths: fallbackProfile.strengths,
    careerSuggestions: fallbackProfile.careerSuggestions,
    developmentAreas,
    workEnvironment: fallbackProfile.workEnvironment
  };
}

// Fungsi untuk mendapatkan level interpretasi skor
export function getScoreLevel(score: number): { level: string; color: string; description: string } {
  if (score >= 8) {
    return {
      level: 'Sangat Tinggi',
      color: 'text-green-600 bg-green-50 border-green-200',
      description: 'Minat yang sangat kuat pada area ini'
    };
  } else if (score >= 6) {
    return {
      level: 'Tinggi',
      color: 'text-blue-600 bg-blue-50 border-blue-200',
      description: 'Minat yang cukup kuat pada area ini'
    };
  } else if (score >= 4) {
    return {
      level: 'Sedang',
      color: 'text-yellow-600 bg-yellow-50 border-yellow-200',
      description: 'Minat yang moderat pada area ini'
    };
  } else {
    return {
      level: 'Rendah',
      color: 'text-gray-600 bg-gray-50 border-gray-200',
      description: 'Minat yang rendah pada area ini'
    };
  }
}
