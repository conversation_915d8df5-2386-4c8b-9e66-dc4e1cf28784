import { Question, RiasecDescription } from './types';

// Sample RIASEC questions (2 per type = 12 total)
export const questions: Question[] = [
  // Realistic (R) - Hands-on, practical, mechanical
  {
    id: 1,
    text: "Saya senang bekerja dengan tangan dan menggunakan alat-alat mekanik",
    riasec_type: 'R'
  },
  {
    id: 2,
    text: "Saya tertarik untuk memperbaiki mesin atau peralatan elektronik",
    riasec_type: 'R'
  },

  // Investigative (I) - Analytical, scientific, research-oriented
  {
    id: 3,
    text: "Saya senang melakukan penelitian dan menganalisis data",
    riasec_type: 'I'
  },
  {
    id: 4,
    text: "Saya tertarik untuk memecahkan masalah yang kompleks secara sistematis",
    riasec_type: 'I'
  },

  // Artistic (A) - Creative, expressive, imaginative
  {
    id: 5,
    text: "Saya senang mengekspresikan kreativitas melalui seni atau desain",
    riasec_type: 'A'
  },
  {
    id: 6,
    text: "Saya tertarik untuk menciptakan karya yang unik dan inovatif",
    riasec_type: 'A'
  },

  // Social (S) - Helping, teaching, caring for others
  {
    id: 7,
    text: "Saya senang membantu orang lain menyelesaikan masalah mereka",
    riasec_type: 'S'
  },
  {
    id: 8,
    text: "Saya tertarik untuk mengajar atau melatih orang lain",
    riasec_type: 'S'
  },

  // Enterprising (E) - Leading, persuading, business-oriented
  {
    id: 9,
    text: "Saya senang memimpin tim dan mengorganisir proyek",
    riasec_type: 'E'
  },
  {
    id: 10,
    text: "Saya tertarik untuk memulai bisnis atau berwirausaha",
    riasec_type: 'E'
  },

  // Conventional (C) - Organizing, detail-oriented, systematic
  {
    id: 11,
    text: "Saya senang bekerja dengan data dan mengatur informasi secara sistematis",
    riasec_type: 'C'
  },
  {
    id: 12,
    text: "Saya tertarik untuk bekerja dalam lingkungan yang terstruktur dan terorganisir",
    riasec_type: 'C'
  }
];

// RIASEC type descriptions
export const riasecDescriptions: RiasecDescription[] = [
  {
    type: 'R',
    name: 'Realistic',
    description: 'Menyukai pekerjaan yang melibatkan aktivitas fisik, menggunakan alat, mesin, atau bekerja di luar ruangan. Cenderung praktis dan suka bekerja dengan tangan.'
  },
  {
    type: 'I',
    name: 'Investigative',
    description: 'Menyukai pekerjaan yang melibatkan penelitian, analisis, dan pemecahan masalah. Cenderung analitis, intelektual, dan suka bekerja dengan ide-ide.'
  },
  {
    type: 'A',
    name: 'Artistic',
    description: 'Menyukai pekerjaan yang melibatkan kreativitas, ekspresi diri, dan inovasi. Cenderung imajinatif, ekspresif, dan suka bekerja dalam lingkungan yang fleksibel.'
  },
  {
    type: 'S',
    name: 'Social',
    description: 'Menyukai pekerjaan yang melibatkan interaksi dengan orang lain, membantu, mengajar, atau memberikan layanan. Cenderung kooperatif, ramah, dan peduli terhadap kesejahteraan orang lain.'
  },
  {
    type: 'E',
    name: 'Enterprising',
    description: 'Menyukai pekerjaan yang melibatkan kepemimpinan, persuasi, dan pengambilan keputusan bisnis. Cenderung ambisius, percaya diri, dan suka mengambil risiko.'
  },
  {
    type: 'C',
    name: 'Conventional',
    description: 'Menyukai pekerjaan yang melibatkan organisasi, detail, dan prosedur yang terstruktur. Cenderung teratur, teliti, dan suka bekerja dengan data atau sistem.'
  }
];

// Likert scale options
export const likertOptions = [
  { value: 1, label: 'Sangat Tidak Setuju' },
  { value: 2, label: 'Tidak Setuju' },
  { value: 3, label: 'Netral' },
  { value: 4, label: 'Setuju' },
  { value: 5, label: 'Sangat Setuju' }
];
