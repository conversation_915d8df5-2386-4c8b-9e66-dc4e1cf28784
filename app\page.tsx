import Link from 'next/link';

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-2xl mx-auto text-center bg-white rounded-2xl shadow-xl p-8 md:p-12">
        <div className="mb-8">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Tes Minat Karir RIASEC
          </h1>
          <div className="w-24 h-1 bg-indigo-500 mx-auto mb-6"></div>
          <p className="text-lg text-gray-600 leading-relaxed">
            Temukan minat karir Anda berdasarkan model RIASEC (Realistic, Investigative,
            Artistic, Social, Enterprising, Conventional). Tes ini akan membantu Anda
            memahami preferensi karir yang sesuai dengan kepribadian Anda.
          </p>
        </div>

        <div className="bg-gray-50 rounded-xl p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Tentang Tes Ini
          </h2>
          <ul className="text-left text-gray-600 space-y-2">
            <li className="flex items-start">
              <span className="text-indigo-500 mr-2">•</span>
              12 pertanyaan singkat tentang preferensi aktivitas
            </li>
            <li className="flex items-start">
              <span className="text-indigo-500 mr-2">•</span>
              Waktu pengerjaan sekitar 5-10 menit
            </li>
            <li className="flex items-start">
              <span className="text-indigo-500 mr-2">•</span>
              Hasil berupa profil minat karir yang personal
            </li>
            <li className="flex items-start">
              <span className="text-indigo-500 mr-2">•</span>
              Visualisasi hasil dalam bentuk radar chart
            </li>
          </ul>
        </div>

        <Link
          href="/test"
          className="inline-block bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-4 px-8 rounded-xl transition-colors duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
        >
          Mulai Tes
        </Link>

        <p className="text-sm text-gray-500 mt-6">
          Jawab semua pertanyaan dengan jujur untuk mendapatkan hasil yang akurat
        </p>
      </div>
    </div>
  );
}
